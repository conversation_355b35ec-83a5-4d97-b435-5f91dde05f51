# Claude Code在Windows系统中原生安装教程

## 🎯 概述

虽然Claude Code官方推荐使用WSL，但实际上可以通过一些方法在Windows上原生安装和运行Claude Code，无需Ubuntu或WSL。

## 📋 系统要求

- **操作系统**: Windows 10/11
- **Node.js**: 18.0.0 或更高版本
- **npm**: 最新版本
- **网络**: 稳定的互联网连接

## 🚀 方法一：绕过平台检查安装

### 第一步：安装Node.js

1. **下载Node.js**
   - 访问 [Node.js官网](https://nodejs.org/)
   - 下载LTS版本（推荐18.x或20.x）
   - 运行安装程序

2. **验证安装**
   ```powershell
   node --version
   npm --version
   ```

### 第二步：配置npm绕过脚本执行

```powershell
# 设置npm配置，忽略包中的脚本执行
npm config set ignore-scripts true

# 设置npm注册表（可选，提高下载速度）
npm config set registry https://registry.npmjs.org/
```

### 第三步：强制安装Claude Code

```powershell
# 方法1：直接安装（可能会失败）
npm install -g @anthropic-ai/claude-code

# 方法2：如果失败，使用强制安装
npm install -g @anthropic-ai/claude-code --force

# 方法3：忽略平台检查
npm install -g @anthropic-ai/claude-code --ignore-platform-reqs
```

### 第四步：手动修复（如果需要）

如果安装后无法运行，可能需要手动修复：

```powershell
# 找到Claude Code安装目录
npm list -g @anthropic-ai/claude-code

# 通常在：
# C:\Users\<USER>\AppData\Roaming\npm\node_modules\@anthropic-ai\claude-code
```

## 🔧 方法二：使用第三方包装器

### 创建Windows包装器脚本

1. **创建批处理文件**
   ```batch
   @echo off
   REM claude.bat
   node "C:\Users\<USER>\AppData\Roaming\npm\node_modules\@anthropic-ai\claude-code\bin\claude.js" %*
   ```

2. **添加到PATH**
   - 将批处理文件放在PATH目录中
   - 或添加包含该文件的目录到PATH

### 创建PowerShell包装器

```powershell
# 创建 claude.ps1
function claude {
    param([Parameter(ValueFromRemainingArguments)]$args)
    & node "C:\Users\<USER>\AppData\Roaming\npm\node_modules\@anthropic-ai\claude-code\bin\claude.js" @args
}
```

## 🛠️ 方法三：Docker方式（推荐）

### 使用Docker运行Claude Code

1. **安装Docker Desktop**
   - 下载并安装Docker Desktop for Windows

2. **创建Dockerfile**
   ```dockerfile
   FROM node:18-alpine
   
   # 安装Claude Code
   RUN npm install -g @anthropic-ai/claude-code
   
   # 设置工作目录
   WORKDIR /workspace
   
   # 暴露端口（如果需要）
   EXPOSE 3000
   
   # 启动命令
   CMD ["claude"]
   ```

3. **构建和运行**
   ```powershell
   # 构建镜像
   docker build -t claude-code .
   
   # 运行容器
   docker run -it --rm -v ${PWD}:/workspace claude-code
   ```

## 🎯 方法四：使用Git Bash或MinGW

### 在Git Bash中安装

```bash
# 在Git Bash中运行
npm install -g @anthropic-ai/claude-code

# 如果失败，尝试
npm install -g @anthropic-ai/claude-code --unsafe-perm=true --allow-root
```

## 🔍 验证安装

### 检查安装状态

```powershell
# 检查Claude Code是否可用
claude --version

# 检查安装位置
where claude

# 或者
npm list -g @anthropic-ai/claude-code
```

### 测试运行

```powershell
# 尝试启动Claude Code
claude

# 如果出现错误，查看详细信息
claude --help
```

## 🚨 常见问题和解决方案

### 问题1：平台不支持错误

**错误信息**: "Claude Code is not supported on Windows"

**解决方案**:
```powershell
# 方法1：忽略平台检查
npm config set target_platform linux
npm install -g @anthropic-ai/claude-code

# 方法2：修改package.json（高级）
# 手动编辑Claude Code的package.json，移除平台限制
```

### 问题2：权限错误

**解决方案**:
```powershell
# 以管理员身份运行PowerShell
# 或者配置npm全局目录
npm config set prefix "C:\npm-global"
# 然后将C:\npm-global\bin添加到PATH
```

### 问题3：Node.js版本问题

**解决方案**:
```powershell
# 使用nvm-windows管理Node.js版本
# 下载：https://github.com/coreybutler/nvm-windows

# 安装特定版本
nvm install 18.19.0
nvm use 18.19.0
```

### 问题4：网络连接问题

**解决方案**:
```powershell
# 配置代理（如果需要）
npm config set proxy http://proxy-server:port
npm config set https-proxy http://proxy-server:port

# 或使用淘宝镜像
npm config set registry https://registry.npmmirror.com/
```

## 🎮 VS Code集成

### 配置VS Code

1. **安装扩展**
   - 不需要WSL扩展
   - 可以直接在Windows环境中使用

2. **配置终端**
   ```json
   // settings.json
   {
     "terminal.integrated.defaultProfile.windows": "PowerShell",
     "terminal.integrated.profiles.windows": {
       "PowerShell": {
         "source": "PowerShell",
         "args": ["-NoExit", "-Command", "& {Set-Location $env:USERPROFILE}"]
       }
     }
   }
   ```

3. **使用Claude Code**
   ```powershell
   # 在VS Code终端中直接运行
   claude
   ```

## 🔐 身份验证

### 配置认证

```powershell
# 启动Claude Code进行首次认证
claude

# 选择认证方式：
# 1. Anthropic Console
# 2. Claude App订阅
# 3. 企业平台
```

## 📊 性能优化

### 提升性能

1. **使用SSD存储**
2. **配置Node.js内存**
   ```powershell
   # 增加Node.js内存限制
   set NODE_OPTIONS=--max-old-space-size=4096
   ```

3. **关闭不必要的安全软件扫描**

## 🎉 完整安装脚本

### 自动化安装脚本

```powershell
# Windows原生Claude Code安装脚本
Write-Host "=== Claude Code Windows Native Installation ===" -ForegroundColor Green

# 检查Node.js
if (!(Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "Please install Node.js first!" -ForegroundColor Red
    Start-Process "https://nodejs.org/"
    exit 1
}

# 配置npm
Write-Host "Configuring npm..." -ForegroundColor Yellow
npm config set ignore-scripts true
npm config set registry https://registry.npmjs.org/

# 安装Claude Code
Write-Host "Installing Claude Code..." -ForegroundColor Yellow
try {
    npm install -g @anthropic-ai/claude-code --force
    Write-Host "Claude Code installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Installation failed, trying alternative method..." -ForegroundColor Yellow
    npm install -g @anthropic-ai/claude-code --ignore-platform-reqs --unsafe-perm=true
}

# 验证安装
Write-Host "Verifying installation..." -ForegroundColor Yellow
try {
    $version = claude --version
    Write-Host "Claude Code version: $version" -ForegroundColor Green
    Write-Host "Installation completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Installation may have issues. Please check manually." -ForegroundColor Yellow
}

Write-Host "You can now run 'claude' in any directory!" -ForegroundColor Cyan
```

## 📝 总结

通过以上方法，您可以在Windows上原生运行Claude Code，无需WSL或Ubuntu：

1. **推荐方法**: Docker方式（最稳定）
2. **简单方法**: 绕过平台检查直接安装
3. **高级方法**: 手动修复和包装器

选择最适合您环境的方法即可开始使用Claude Code！
