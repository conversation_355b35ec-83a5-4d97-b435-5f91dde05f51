# Setup Claude Code for Windows
Write-Host "=== Setting up Claude Code for Windows ===" -ForegroundColor Green

# Set environment variables for Windows compatibility
$env:SHELL = "powershell"
$env:CLAUDECODE = "1"

Write-Host "Environment variables set:" -ForegroundColor Cyan
Write-Host "SHELL = $env:SHELL" -ForegroundColor Gray
Write-Host "CLAUDECODE = $env:CLAUDECODE" -ForegroundColor Gray

# Try to start Claude Code with Windows-friendly settings
Write-Host "`nStarting Claude Code..." -ForegroundColor Yellow
Write-Host "If this is your first time, you'll need to authenticate." -ForegroundColor Cyan
Write-Host "Choose 'Anthropic Console' for the easiest setup." -ForegroundColor Cyan

# Start Claude Code
.\claude.bat
