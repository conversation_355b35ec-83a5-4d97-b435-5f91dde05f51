# Claude Code 使用指南

## 🎯 快速开始

### 启动Claude Code
```cmd
.\claude.bat
```

### 常用命令
```cmd
# 查看版本
.\claude.bat --version

# 查看帮助
.\claude.bat --help

# 运行诊断
.\claude.bat doctor

# 配置设置
.\claude.bat config
```

## 🔐 首次使用 - 身份验证

1. **启动Claude Code**
   ```cmd
   .\claude.bat
   ```

2. **选择认证方式**
   - **Anthropic Console** (推荐) - 需要API账户
   - **Claude App订阅** - 需要Pro或Max订阅
   - **企业平台** - AWS Bedrock或Google Vertex AI

3. **完成认证流程**
   - 按照提示在浏览器中完成OAuth
   - 返回终端继续使用

## 💻 在VS Code中使用

1. **打开项目**
   ```cmd
   code .
   ```

2. **在VS Code终端中使用**
   - 按 `Ctrl + `` 打开终端
   - 运行: `.\claude.bat`

3. **开始AI编程**
   - 询问代码问题
   - 请求代码审查
   - 生成代码片段
   - 解释复杂逻辑

## 🚀 实用功能

### 代码分析
```
请分析这个函数的性能问题
```

### 代码生成
```
帮我写一个React组件，用于显示用户列表
```

### 代码审查
```
请审查这段代码，指出可能的问题
```

### 调试帮助
```
这个错误是什么意思？如何修复？
```

## ⚙️ 配置选项

### 查看当前配置
```cmd
.\claude.bat config
```

### 常用配置
```cmd
# 设置主题
.\claude.bat config set theme dark

# 设置模型
.\claude.bat config set model sonnet

# 查看所有设置
.\claude.bat config list
```

## 🔧 高级功能

### 继续对话
```cmd
.\claude.bat --continue
```

### 恢复会话
```cmd
.\claude.bat --resume
```

### 非交互模式
```cmd
.\claude.bat --print "解释这段代码"
```

### 指定模型
```cmd
.\claude.bat --model sonnet
```

## 📁 项目集成

### 自动连接IDE
```cmd
.\claude.bat --ide
```

### 添加目录访问
```cmd
.\claude.bat --add-dir ./src ./docs
```

## 🆘 故障排除

### 常见问题

1. **认证失败**
   - 检查网络连接
   - 重新运行认证流程
   - 确认账户状态

2. **命令未找到**
   - 使用 `.\claude.bat` 而不是 `claude`
   - 确认在正确目录

3. **权限错误**
   - 以管理员身份运行PowerShell
   - 检查文件权限

### 重置配置
```cmd
.\claude.bat config reset
```

### 更新Claude Code
```cmd
.\claude.bat update
```

## 💡 使用技巧

1. **明确的问题** - 提供具体的上下文
2. **代码片段** - 包含相关代码
3. **期望结果** - 说明想要达到的目标
4. **错误信息** - 提供完整的错误日志

## 🎉 开始使用

现在您可以开始享受AI辅助编程的强大功能了！

```cmd
# 启动Claude Code
.\claude.bat

# 开始对话
你好！我想学习如何使用Claude Code进行编程。
```

祝您编程愉快！🚀
