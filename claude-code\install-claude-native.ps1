# Claude Code Native Windows Installation
# No WSL or Ubuntu required

Write-Host "=== Claude Code Native Windows Installation ===" -ForegroundColor Green
Write-Host "Installing Claude Code directly on Windows without WSL" -ForegroundColor Cyan

# Step 1: Check Node.js
Write-Host "`nStep 1: Checking Node.js..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version 2>$null
    $npmVersion = npm --version 2>$null
    
    if ($nodeVersion -and $npmVersion) {
        Write-Host "OK Node.js installed: $nodeVersion" -ForegroundColor Green
        Write-Host "OK npm installed: $npmVersion" -ForegroundColor Green
    } else {
        Write-Host "ERROR Node.js not installed" -ForegroundColor Red
        Write-Host "Opening Node.js download page..." -ForegroundColor Cyan
        Start-Process "https://nodejs.org/"
        Write-Host "Please install Node.js and run this script again" -ForegroundColor Yellow
        pause
        exit 1
    }
} catch {
    Write-Host "ERROR Cannot check Node.js" -ForegroundColor Red
    exit 1
}

# Step 2: Configure npm
Write-Host "`nStep 2: Configuring npm..." -ForegroundColor Yellow

try {
    Write-Host "Setting npm configuration..." -ForegroundColor Cyan
    
    npm config set ignore-scripts true
    Write-Host "OK Set ignore-scripts" -ForegroundColor Green
    
    npm config set registry https://registry.npmjs.org/
    Write-Host "OK Set registry" -ForegroundColor Green
    
    npm config set target_platform linux
    Write-Host "OK Set target platform" -ForegroundColor Green
    
} catch {
    Write-Host "WARNING npm configuration may have issues" -ForegroundColor Yellow
}

# Step 3: Install Claude Code
Write-Host "`nStep 3: Installing Claude Code..." -ForegroundColor Yellow

$installSuccess = $false

# Method 1: Standard install
Write-Host "Trying standard installation..." -ForegroundColor Cyan
try {
    npm install -g @anthropic-ai/claude-code
    $claudeVersion = claude --version 2>$null
    if ($claudeVersion) {
        Write-Host "OK Standard installation successful!" -ForegroundColor Green
        Write-Host "Claude Code version: $claudeVersion" -ForegroundColor Cyan
        $installSuccess = $true
    }
} catch {
    Write-Host "Standard installation failed, trying force install..." -ForegroundColor Yellow
}

# Method 2: Force install
if (-not $installSuccess) {
    Write-Host "Trying force installation..." -ForegroundColor Cyan
    try {
        npm install -g @anthropic-ai/claude-code --force
        $claudeVersion = claude --version 2>$null
        if ($claudeVersion) {
            Write-Host "OK Force installation successful!" -ForegroundColor Green
            Write-Host "Claude Code version: $claudeVersion" -ForegroundColor Cyan
            $installSuccess = $true
        }
    } catch {
        Write-Host "Force installation failed, trying platform bypass..." -ForegroundColor Yellow
    }
}

# Method 3: Platform bypass
if (-not $installSuccess) {
    Write-Host "Trying platform bypass installation..." -ForegroundColor Cyan
    try {
        npm install -g @anthropic-ai/claude-code --ignore-platform-reqs --force --unsafe-perm=true
        $claudeVersion = claude --version 2>$null
        if ($claudeVersion) {
            Write-Host "OK Platform bypass successful!" -ForegroundColor Green
            Write-Host "Claude Code version: $claudeVersion" -ForegroundColor Cyan
            $installSuccess = $true
        }
    } catch {
        Write-Host "Platform bypass failed" -ForegroundColor Red
    }
}

# Step 4: Create wrapper if needed
if (-not $installSuccess) {
    Write-Host "`nStep 4: Creating wrapper scripts..." -ForegroundColor Yellow
    
    # Check if Claude Code was installed but not in PATH
    $claudePath = "$env:APPDATA\npm\node_modules\@anthropic-ai\claude-code\bin\claude.js"
    if (Test-Path $claudePath) {
        Write-Host "Found Claude Code installation, creating wrapper..." -ForegroundColor Cyan
        
        # Create batch wrapper
        $batchWrapper = @"
@echo off
node "$env:APPDATA\npm\node_modules\@anthropic-ai\claude-code\bin\claude.js" %*
"@
        $batchWrapper | Out-File -FilePath "claude.bat" -Encoding ASCII
        
        # Create PowerShell wrapper
        $psWrapper = @"
function claude {
    param([Parameter(ValueFromRemainingArguments)]`$args)
    & node "$env:APPDATA\npm\node_modules\@anthropic-ai\claude-code\bin\claude.js" @args
}
"@
        $psWrapper | Out-File -FilePath "claude.ps1" -Encoding UTF8
        
        Write-Host "OK Wrapper scripts created" -ForegroundColor Green
        Write-Host "Usage:" -ForegroundColor Cyan
        Write-Host "  Batch: claude.bat" -ForegroundColor White
        Write-Host "  PowerShell: . .\claude.ps1 then run claude" -ForegroundColor White
        
        $installSuccess = $true
    } else {
        Write-Host "ERROR Claude Code installation not found" -ForegroundColor Red
    }
}

# Step 5: Verification
Write-Host "`nStep 5: Verification..." -ForegroundColor Yellow

if ($installSuccess) {
    Write-Host "OK Claude Code installation completed!" -ForegroundColor Green
    
    # Test Claude Code
    try {
        Write-Host "Testing Claude Code..." -ForegroundColor Cyan
        $helpOutput = claude --help 2>$null
        if ($helpOutput) {
            Write-Host "OK Claude Code is working" -ForegroundColor Green
        } else {
            Write-Host "WARNING Claude Code may need manual configuration" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "WARNING Verification had issues but installation may be successful" -ForegroundColor Yellow
    }
    
    Write-Host "`nSUCCESS! Claude Code is now installed on Windows!" -ForegroundColor Green
    Write-Host "`nUsage:" -ForegroundColor Cyan
    Write-Host "1. Open PowerShell or CMD" -ForegroundColor White
    Write-Host "2. Navigate to project: cd E:\2025\20250710" -ForegroundColor White
    Write-Host "3. Run Claude Code: claude" -ForegroundColor White
    Write-Host "4. Follow authentication prompts" -ForegroundColor White
    
    Write-Host "`nAdvantages:" -ForegroundColor Cyan
    Write-Host "- No WSL or Ubuntu needed" -ForegroundColor Green
    Write-Host "- Native Windows performance" -ForegroundColor Green
    Write-Host "- Full VS Code integration" -ForegroundColor Green
    Write-Host "- Works with all Windows terminals" -ForegroundColor Green
    
} else {
    Write-Host "ERROR All installation methods failed" -ForegroundColor Red
    Write-Host "Suggestions:" -ForegroundColor Yellow
    Write-Host "1. Run as Administrator" -ForegroundColor White
    Write-Host "2. Check Node.js version (requires 18+)" -ForegroundColor White
    Write-Host "3. Check internet connection" -ForegroundColor White
    Write-Host "4. Try manual installation from tutorial" -ForegroundColor White
}

Write-Host "`nDocumentation:" -ForegroundColor Cyan
Write-Host "- Tutorial: Claude-Code-Windows-Native-Installation-Tutorial.md" -ForegroundColor White

pause
