# Claude Code Windows 原生安装脚本
# 无需WSL或Ubuntu，直接在Windows上安装Claude Code

param(
    [switch]$Force,
    [switch]$UseDocker,
    [switch]$SkipNodeCheck
)

Write-Host "=== Claude Code Windows 原生安装 ===" -ForegroundColor Green
Write-Host "无需WSL或Ubuntu，直接在Windows上安装" -ForegroundColor Cyan
Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Gray

# 第一步：检查Node.js
if (-not $SkipNodeCheck) {
    Write-Host "`n🔍 第一步：检查Node.js环境" -ForegroundColor Yellow
    
    try {
        $nodeVersion = node --version 2>$null
        $npmVersion = npm --version 2>$null
        
        if ($nodeVersion -and $npmVersion) {
            Write-Host "✅ Node.js已安装: $nodeVersion" -ForegroundColor Green
            Write-Host "✅ npm已安装: $npmVersion" -ForegroundColor Green
            
            # 检查Node.js版本是否满足要求
            $versionNumber = [version]($nodeVersion -replace 'v', '')
            if ($versionNumber -lt [version]"18.0.0") {
                Write-Host "⚠️  Node.js版本过低，建议升级到18.0.0或更高版本" -ForegroundColor Yellow
                $upgrade = Read-Host "是否现在升级Node.js？(y/n)"
                if ($upgrade -eq "y" -or $upgrade -eq "Y") {
                    Start-Process "https://nodejs.org/"
                    Write-Host "请下载并安装最新版本的Node.js，然后重新运行此脚本" -ForegroundColor Cyan
                    pause
                    exit 0
                }
            }
        } else {
            Write-Host "❌ Node.js未安装" -ForegroundColor Red
            Write-Host "正在打开Node.js下载页面..." -ForegroundColor Cyan
            Start-Process "https://nodejs.org/"
            Write-Host "请安装Node.js后重新运行此脚本" -ForegroundColor Yellow
            pause
            exit 1
        }
    } catch {
        Write-Host "❌ 无法检查Node.js状态" -ForegroundColor Red
        exit 1
    }
}

# 第二步：Docker方式安装（可选）
if ($UseDocker) {
    Write-Host "`n🐳 第二步：使用Docker安装Claude Code" -ForegroundColor Yellow
    
    # 检查Docker
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-Host "✅ Docker已安装: $dockerVersion" -ForegroundColor Green
            
            # 创建Dockerfile
            $dockerfile = @"
FROM node:18-alpine

# 安装Claude Code
RUN npm install -g @anthropic-ai/claude-code

# 设置工作目录
WORKDIR /workspace

# 启动命令
CMD ["claude"]
"@
            
            $dockerfile | Out-File -FilePath "Dockerfile" -Encoding UTF8
            Write-Host "✅ Dockerfile已创建" -ForegroundColor Green
            
            # 构建镜像
            Write-Host "构建Docker镜像..." -ForegroundColor Cyan
            docker build -t claude-code .
            
            # 创建启动脚本
            $dockerScript = @"
@echo off
docker run -it --rm -v "%CD%":/workspace claude-code %*
"@
            $dockerScript | Out-File -FilePath "claude.bat" -Encoding ASCII
            
            Write-Host "✅ Docker方式安装完成" -ForegroundColor Green
            Write-Host "使用方法：运行 claude.bat" -ForegroundColor Cyan
            pause
            exit 0
            
        } else {
            Write-Host "❌ Docker未安装，切换到原生安装方式" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Docker检查失败，使用原生安装方式" -ForegroundColor Yellow
    }
}

# 第三步：配置npm环境
Write-Host "`n⚙️  第三步：配置npm环境" -ForegroundColor Yellow

try {
    Write-Host "配置npm设置..." -ForegroundColor Cyan
    
    # 设置忽略脚本执行
    npm config set ignore-scripts true
    Write-Host "✅ 已设置忽略脚本执行" -ForegroundColor Green
    
    # 设置注册表
    npm config set registry https://registry.npmjs.org/
    Write-Host "✅ 已设置npm注册表" -ForegroundColor Green
    
    # 设置平台（绕过平台检查）
    npm config set target_platform linux
    Write-Host "✅ 已设置目标平台" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️  npm配置可能有问题，但继续安装..." -ForegroundColor Yellow
}

# 第四步：安装Claude Code
Write-Host "`n🤖 第四步：安装Claude Code" -ForegroundColor Yellow

$installSuccess = $false
$installMethods = @(
    @{
        Name = "标准安装"
        Command = "npm install -g @anthropic-ai/claude-code"
    },
    @{
        Name = "强制安装"
        Command = "npm install -g @anthropic-ai/claude-code --force"
    },
    @{
        Name = "忽略平台检查"
        Command = "npm install -g @anthropic-ai/claude-code --ignore-platform-reqs"
    },
    @{
        Name = "完全绕过检查"
        Command = "npm install -g @anthropic-ai/claude-code --force --ignore-platform-reqs --unsafe-perm=true"
    }
)

foreach ($method in $installMethods) {
    if ($installSuccess) { break }
    
    Write-Host "尝试方法：$($method.Name)" -ForegroundColor Cyan
    try {
        Invoke-Expression $method.Command
        
        # 验证安装
        $claudeVersion = claude --version 2>$null
        if ($claudeVersion) {
            Write-Host "✅ $($method.Name) 成功！" -ForegroundColor Green
            Write-Host "Claude Code版本: $claudeVersion" -ForegroundColor Cyan
            $installSuccess = $true
        } else {
            Write-Host "⚠️  $($method.Name) 可能失败，尝试下一种方法..." -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($method.Name) 失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 第五步：创建包装器（如果直接安装失败）
if (-not $installSuccess) {
    Write-Host "`n🔧 第五步：创建Windows包装器" -ForegroundColor Yellow
    
    # 查找Claude Code安装位置
    $claudePath = npm list -g @anthropic-ai/claude-code 2>$null
    if ($claudePath) {
        Write-Host "找到Claude Code安装位置" -ForegroundColor Cyan
        
        # 创建批处理包装器
        $batchWrapper = @"
@echo off
node "%APPDATA%\npm\node_modules\@anthropic-ai\claude-code\bin\claude.js" %*
"@
        
        $batchWrapper | Out-File -FilePath "claude.bat" -Encoding ASCII
        
        # 创建PowerShell包装器
        $psWrapper = @"
function claude {
    param([Parameter(ValueFromRemainingArguments)]`$args)
    & node "`$env:APPDATA\npm\node_modules\@anthropic-ai\claude-code\bin\claude.js" @args
}
"@
        
        $psWrapper | Out-File -FilePath "claude.ps1" -Encoding UTF8
        
        Write-Host "✅ 包装器脚本已创建" -ForegroundColor Green
        Write-Host "使用方法：" -ForegroundColor Cyan
        Write-Host "  - 批处理：claude.bat" -ForegroundColor White
        Write-Host "  - PowerShell：. .\claude.ps1 然后运行 claude" -ForegroundColor White
        
        $installSuccess = $true
    }
}

# 第六步：验证安装
Write-Host "`n🔍 第六步：验证安装" -ForegroundColor Yellow

if ($installSuccess) {
    try {
        # 测试Claude Code
        Write-Host "测试Claude Code..." -ForegroundColor Cyan
        
        # 尝试运行帮助命令
        $helpOutput = claude --help 2>$null
        if ($helpOutput) {
            Write-Host "✅ Claude Code运行正常" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Claude Code可能需要手动配置" -ForegroundColor Yellow
        }
        
        # 检查安装位置
        $claudeLocation = where.exe claude 2>$null
        if ($claudeLocation) {
            Write-Host "Claude Code位置: $claudeLocation" -ForegroundColor Cyan
        }
        
    } catch {
        Write-Host "⚠️  验证过程中出现问题，但安装可能成功" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ 所有安装方法都失败了" -ForegroundColor Red
    Write-Host "建议尝试以下解决方案：" -ForegroundColor Yellow
    Write-Host "1. 以管理员身份运行此脚本" -ForegroundColor White
    Write-Host "2. 使用Docker方式：.\Claude-Code-Windows-原生安装.ps1 -UseDocker" -ForegroundColor White
    Write-Host "3. 手动按照教程安装：Claude-Code-Windows-原生安装教程.md" -ForegroundColor White
}

# 第七步：VS Code集成提示
Write-Host "`n💻 第七步：VS Code集成" -ForegroundColor Yellow

Write-Host "VS Code集成说明：" -ForegroundColor Cyan
Write-Host "1. 无需安装WSL扩展" -ForegroundColor White
Write-Host "2. 直接在VS Code终端中运行 claude" -ForegroundColor White
Write-Host "3. 支持PowerShell、CMD、Windows Terminal" -ForegroundColor White

# 完成总结
Write-Host "`n🎉 安装完成！" -ForegroundColor Green
Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Gray

if ($installSuccess) {
    Write-Host "`n✨ Claude Code已成功安装到Windows！" -ForegroundColor Green
    Write-Host "`n📖 使用方法：" -ForegroundColor Cyan
    Write-Host "1. 打开PowerShell或CMD" -ForegroundColor White
    Write-Host "2. 导航到项目目录：cd E:\2025\20250710" -ForegroundColor White
    Write-Host "3. 运行Claude Code：claude" -ForegroundColor White
    Write-Host "4. 按照提示完成身份验证" -ForegroundColor White
    
    Write-Host "`n🎯 优势：" -ForegroundColor Cyan
    Write-Host "✅ 无需WSL或Ubuntu" -ForegroundColor Green
    Write-Host "✅ 原生Windows性能" -ForegroundColor Green
    Write-Host "✅ 完整VS Code集成" -ForegroundColor Green
    Write-Host "✅ 支持所有Windows终端" -ForegroundColor Green
    
} else {
    Write-Host "`n⚠️  安装可能未完全成功" -ForegroundColor Yellow
    Write-Host "请查看详细教程：Claude-Code-Windows-原生安装教程.md" -ForegroundColor Cyan
}

Write-Host "`n📚 相关文件：" -ForegroundColor Cyan
Write-Host "- 详细教程：Claude-Code-Windows-原生安装教程.md" -ForegroundColor White
Write-Host "- 包装器脚本：claude.bat, claude.ps1" -ForegroundColor White

# 清理临时文件
Remove-Item "Dockerfile" -ErrorAction SilentlyContinue

pause
